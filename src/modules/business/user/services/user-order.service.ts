import { Injectable, Logger } from '@nestjs/common';
import { UserOrderRepository } from '@modules/business/repositories';
import { PaginatedResult } from '@common/response';
import { QueryUserOrderDto, UserOrderListItemDto, UserOrderResponseDto, UserOrderStatusResponseDto, OrderStatusStatsDto, ShippingStatusStatsDto } from '../dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { OrderStatusEnum, ShippingStatusEnum } from '../../enums';

/**
 * Service xử lý logic nghiệp vụ cho đơn hàng của người dùng
 */
@Injectable()
export class UserOrderService {
  private readonly logger = new Logger(UserOrderService.name);

  constructor(
    private readonly userOrderRepository: UserOrderRepository,
  ) {}

  /**
   * L<PERSON>y danh sách đơn hàng của người dùng với phân trang
   * @param userId ID người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách đơn hàng với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserOrderDto): Promise<PaginatedResult<UserOrderListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách đơn hàng cho userId=${userId}`);

      // Lấy danh sách đơn hàng từ repository
      const result = await this.userOrderRepository.findAll(userId, queryDto);

      // Chuyển đổi sang DTO response
      const items = result.items.map(item => {
        return plainToInstance(UserOrderListItemDto, item, { excludeExtraneousValues: true });
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách đơn hàng: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy danh sách đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID đơn hàng
   * @param userId ID người dùng
   * @returns Chi tiết đơn hàng
   */
  async findById(id: number, userId: number): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết đơn hàng id=${id} cho userId=${userId}`);

      // Lấy đơn hàng từ repository
      const order = await this.userOrderRepository.findById(id);

      // Kiểm tra đơn hàng tồn tại
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          `Không tìm thấy đơn hàng với ID ${id}`
        );
      }

      // Kiểm tra đơn hàng thuộc về người dùng
      if (order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
          `Bạn không có quyền truy cập đơn hàng này`
        );
      }

      // Chuyển đổi sang DTO response
      return plainToInstance(UserOrderResponseDto, order, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy chi tiết đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Lấy thống kê trạng thái đơn hàng và vận chuyển của người dùng
   * @param userId ID người dùng
   * @returns Thống kê trạng thái đơn hàng và vận chuyển
   */
  async getOrderStatusStats(userId: number): Promise<UserOrderStatusResponseDto> {
    try {
      this.logger.log(`Lấy thống kê trạng thái đơn hàng cho userId=${userId}`);

      // Lấy thống kê từ repository
      const stats = await this.userOrderRepository.getOrderStatusStats(userId);

      // Khởi tạo các giá trị mặc định cho order status
      const orderStatusStats: OrderStatusStatsDto = {
        pending: stats.orderStatus[OrderStatusEnum.PENDING] || 0,
        confirmed: stats.orderStatus[OrderStatusEnum.CONFIRMED] || 0,
        processing: stats.orderStatus[OrderStatusEnum.PROCESSING] || 0,
        completed: stats.orderStatus[OrderStatusEnum.COMPLETED] || 0,
        cancelled: stats.orderStatus[OrderStatusEnum.CANCELLED] || 0,
        total: 0,
      };

      // Tính tổng order status
      orderStatusStats.total = orderStatusStats.pending + orderStatusStats.confirmed +
                               orderStatusStats.processing + orderStatusStats.completed +
                               orderStatusStats.cancelled;

      // Khởi tạo các giá trị mặc định cho shipping status
      const shippingStatusStats: ShippingStatusStatsDto = {
        pending: stats.shippingStatus[ShippingStatusEnum.PENDING] || 0,
        shipped: (stats.shippingStatus[ShippingStatusEnum.DELIVERING] || 0) +
                 (stats.shippingStatus[ShippingStatusEnum.TRANSPORTING] || 0) +
                 (stats.shippingStatus[ShippingStatusEnum.SORTING] || 0) +
                 (stats.shippingStatus[ShippingStatusEnum.PICKING] || 0) +
                 (stats.shippingStatus[ShippingStatusEnum.PICKED] || 0),
        delivered: stats.shippingStatus[ShippingStatusEnum.DELIVERED] || 0,
        cancelled: stats.shippingStatus[ShippingStatusEnum.CANCELLED] || 0,
        deliveryFailed: stats.shippingStatus[ShippingStatusEnum.DELIVERY_FAILED] || 0,
        returning: (stats.shippingStatus[ShippingStatusEnum.RETURNING] || 0) +
                   (stats.shippingStatus[ShippingStatusEnum.RETURNED] || 0),
        total: 0,
      };

      // Tính tổng shipping status
      shippingStatusStats.total = shippingStatusStats.pending + shippingStatusStats.shipped +
                                  shippingStatusStats.delivered + shippingStatusStats.cancelled +
                                  shippingStatusStats.deliveryFailed + shippingStatusStats.returning;

      // Tạo response DTO
      const response: UserOrderStatusResponseDto = {
        orderStatus: orderStatusStats,
        shippingStatus: shippingStatusStats,
      };

      return plainToInstance(UserOrderStatusResponseDto, response, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`
      );
    }
  }
}
