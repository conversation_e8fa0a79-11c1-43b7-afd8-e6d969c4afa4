import { Injectable, Logger } from '@nestjs/common';
import {
  UserProductRepository,
  CustomFieldRepository,
  InventoryRepository,
  PhysicalWarehouseRepository,
} from '@modules/business/repositories';
import {
  BusinessCreateProductDto,
  BusinessUpdateProductDto,
  ProductResponseDto,
  QueryProductDto,
  ClassificationResponseDto,
  CreateClassificationDto,
  BulkDeleteProductDto,
  BulkDeleteProductResponseDto,
  ProductInventoryDto,
  WarehouseListDto,
} from '../dto';
import {
  InventoryResponseDto,
  QueryInventoryDto,
} from '../dto/inventory';
import { EntityStatusEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct, Inventory } from '@modules/business/entities';
import { Transactional } from 'typeorm-transactional';
import { PaginatedResult } from '@common/response';
import { UserProductHelper } from '../helpers/user-product.helper';
import { MetadataHelper } from '../helpers/metadata.helper';
import { ValidationHelper } from '../helpers/validation.helper';
import { S3Service } from '@shared/services/s3.service';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { FileSizeEnum, ImageTypeEnum, TimeIntervalEnum } from '@shared/utils';
import { ClassificationService } from './classification.service';

/**
 * Service xử lý logic nghiệp vụ cho sản phẩm của người dùng
 */
@Injectable()
export class UserProductService {
  private readonly logger = new Logger(UserProductService.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly inventoryRepository: InventoryRepository,
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly userProductHelper: UserProductHelper,
    private readonly metadataHelper: MetadataHelper,
    private readonly s3Service: S3Service,
    private readonly classificationService: ClassificationService,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo sản phẩm mới
   * @param createProductDto DTO chứa thông tin sản phẩm mới
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo
   */
  @Transactional()
  async createProduct(
    createProductDto: BusinessCreateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    try {
      // Kiểm tra giá sản phẩm theo loại giá
      this.validationHelper.validateProductPrice(createProductDto.price, createProductDto.typePrice);

      // Xử lý custom fields nếu có
      let customFields: any[] = [];
      if (createProductDto.customFields && createProductDto.customFields.length > 0) {
        this.logger.log(`Xử lý ${createProductDto.customFields.length} custom fields cho sản phẩm`);

        // Lấy danh sách ID custom fields
        const customFieldIds = this.metadataHelper.extractCustomFieldIds(createProductDto.customFields);

        // Lấy thông tin chi tiết custom fields từ database
        customFields = await this.customFieldRepository.findByIds(customFieldIds);

        // Validate custom fields
        this.metadataHelper.validateCustomFieldInputs(createProductDto.customFields, customFields);
      }

      // Tạo metadata cho sản phẩm
      const metadata = this.metadataHelper.buildMetadata(
        createProductDto.customFields,
        customFields
      );

      // Tạo sản phẩm mới
      const product = new UserProduct();
      product.name = createProductDto.name;
      product.price = createProductDto.price;
      product.typePrice = createProductDto.typePrice;
      product.description = createProductDto.description || '';
      product.images = []; // Khởi tạo mảng rỗng, sẽ được cập nhật sau khi upload
      product.tags = createProductDto.tags || [];
      product.metadata = metadata;
      product.createdBy = userId;
      product.createdAt = Date.now();
      product.updatedAt = Date.now();
      product.status = EntityStatusEnum.PENDING;
      // Đặt các trường embedding là null để tránh lỗi vector dimension
      product.nameEmbedding = null;
      product.descriptionEmbedding = null;
      product.tagsEmbedding = null;

      // Cấu hình vận chuyển mặc định nếu không được cung cấp
      product.shipmentConfig = createProductDto.shipmentConfig || {
        widthCm: 25,
        heightCm: 5,
        lengthCm: 30,
        weightGram: 200,
      };

      // Xử lý và tạo URL upload cho hình ảnh nếu có
      const imageEntries: Array<{ key: string; position: number }> = [];
      const imagesUploadUrls: Array<{ url: string; key: string; index: number }> = [];
      const now = Date.now();

      if (createProductDto.imagesMediaTypes && createProductDto.imagesMediaTypes.length > 0) {
        for (let i = 0; i < createProductDto.imagesMediaTypes.length; i++) {
          try {
            const mediaType = createProductDto.imagesMediaTypes[i] as ImageTypeEnum;

            // Tạo tên file với vị trí và timestamp
            const fileName = `product-image-${i}-${now}`;

            // Sử dụng phương thức createImageUploadUrl
            const imageUploadUrl = await this.createImageUploadUrl(
              fileName,
              mediaType
            );

            const key = imageUploadUrl.key;
            const url = imageUploadUrl.url;

            this.logger.debug(`Created presigned URL for image upload: ${key} with position ${i}`);

            // Lưu key và position vào mảng để cập nhật database
            imageEntries.push({
              key: key,
              position: i
            });

            imagesUploadUrls.push({
              url: url,
              key: key,
              index: i
            });
          } catch (error) {
            this.logger.error(`Failed to create image upload URL at index ${i}: ${error.message}`, error.stack);
          }
        }
      }

      // Cập nhật danh sách ảnh vào sản phẩm
      product.images = imageEntries;

      // Đã loại bỏ xử lý các trường tùy chỉnh vì không tồn tại trong database

      // Lưu sản phẩm vào database
      const savedProduct = await this.userProductRepository.save(product);

      // Xử lý phân loại sản phẩm nếu có
      let classifications: ClassificationResponseDto[] = [];
      if (createProductDto.classifications && createProductDto.classifications.length > 0) {
        try {
          // Tạo các phân loại cho sản phẩm
          classifications = await Promise.all(
            createProductDto.classifications.map(classificationDto =>
              this.classificationService.create(savedProduct.id, classificationDto, userId)
            )
          );
        } catch (classificationError) {
          // Ghi log nhưng không dừng việc tạo sản phẩm
          this.logger.warn(`Không thể tạo phân loại cho sản phẩm ${savedProduct.id}: ${classificationError.message}`);
        }
      }

      // Xử lý tạo tồn kho nếu có thông tin inventory
      let inventory: InventoryResponseDto | undefined;
      if (createProductDto.inventory) {
        try {
          inventory = await this.createOrUpdateProductInventory(
            savedProduct.id,
            createProductDto.inventory,
            userId
          );
        } catch (inventoryError) {
          // Ghi log nhưng không dừng việc tạo sản phẩm
          this.logger.warn(`Không thể tạo tồn kho cho sản phẩm ${savedProduct.id}: ${inventoryError.message}`);
        }
      }

      // Chuyển đổi thành DTO response và trả về
      const productResponse = await this.userProductHelper.mapToProductResponseDto(savedProduct);

      // Thêm thông tin URL upload, phân loại và tồn kho vào response
      return {
        ...productResponse,
        uploadUrls: {
          productId: savedProduct.id.toString(),
          imagesUploadUrls
        },
        classifications: classifications.length > 0 ? classifications : undefined,
        inventory: inventory
      };
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(`Lỗi khi tạo sản phẩm: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo sản phẩm: ${error.message}`,
      );
    }
  }

  // Đã loại bỏ phương thức validateAndProcessCustomFields vì không còn cần thiết

  /**
   * Lấy danh sách sản phẩm
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách sản phẩm với phân trang
   */
  async getProducts(
    queryDto: QueryProductDto,
  ): Promise<PaginatedResult<ProductResponseDto>> {
    try {
      // Lấy danh sách sản phẩm từ repository
      const productsResult =
        await this.userProductRepository.findProducts(queryDto);

      // Chuyển đổi từ entity sang DTO
      const items = await Promise.all(
        productsResult.items.map((product) =>
          this.userProductHelper.mapToProductResponseDto(product),
        ),
      );

      // Trả về kết quả
      return {
        items,
        meta: productsResult.meta,
      };
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(
        `Lỗi khi lấy danh sách sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy danh sách sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết sản phẩm theo ID
   * @param id ID của sản phẩm
   * @returns Chi tiết sản phẩm
   */
  async getProductDetail(id: number): Promise<ProductResponseDto> {
    try {
      // Tìm sản phẩm theo ID
      const product = await this.userProductRepository.findById(id);

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Chuyển đổi từ entity sang DTO
      const productDto = await this.userProductHelper.mapToProductResponseDto(product);

      // Lấy danh sách phân loại sản phẩm nếu có
      try {
        const classifications = await this.classificationService.getByProductId(id);
        if (classifications && classifications.length > 0) {
          // Thêm danh sách phân loại vào DTO
          productDto['classifications'] = classifications;
        }
      } catch (classificationError) {
        // Ghi log nhưng không dừng việc trả về sản phẩm
        this.logger.warn(`Không thể lấy danh sách phân loại cho sản phẩm ${id}: ${classificationError.message}`);
      }

      return productDto;
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(
        `Lỗi khi lấy chi tiết sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Tạo nhiều sản phẩm cùng lúc
   * @param batchCreateDto DTO chứa danh sách sản phẩm cần tạo
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả tạo batch với thông tin thành công và thất bại
   */
  async batchCreateProducts(
    batchCreateDto: { products: BusinessCreateProductDto[] },
    userId: number,
  ): Promise<any> {
    try {
      this.logger.log(`Bắt đầu tạo batch ${batchCreateDto.products.length} sản phẩm cho userId=${userId}`);

      const successProducts: any[] = [];
      const failedProducts: Array<{
        index: number;
        productName: string;
        error: string;
      }> = [];

      // Xử lý từng sản phẩm một cách tuần tự để tránh conflict
      for (let i = 0; i < batchCreateDto.products.length; i++) {
        const productDto = batchCreateDto.products[i];

        try {
          this.logger.log(`Đang tạo sản phẩm ${i + 1}/${batchCreateDto.products.length}: ${productDto.name}`);

          // Tạo sản phẩm sử dụng method createProduct hiện có
          const createdProduct = await this.createProduct(productDto, userId);

          successProducts.push(createdProduct);
          this.logger.log(`Tạo thành công sản phẩm: ${productDto.name}`);

        } catch (error) {
          this.logger.error(`Lỗi khi tạo sản phẩm ${productDto.name}: ${error.message}`, error.stack);

          // Lấy thông điệp lỗi từ AppException hoặc lỗi khác
          const errorMessage = error instanceof AppException
            ? error.message
            : `Lỗi không xác định: ${error.message}`;

          failedProducts.push({
            index: i,
            productName: productDto.name,
            error: errorMessage
          });
        }
      }

      const result = {
        successProducts,
        failedProducts,
        totalProducts: batchCreateDto.products.length,
        successCount: successProducts.length,
        failedCount: failedProducts.length
      };

      this.logger.log(`Hoàn thành batch create: ${result.successCount}/${result.totalProducts} thành công`);

      return result;

    } catch (error) {
      this.logger.error(`Lỗi khi tạo batch sản phẩm: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo batch sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật sản phẩm
   * @param id ID của sản phẩm cần cập nhật
   * @param updateProductDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async updateProduct(
    id: number,
    updateProductDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    try {
      // Kiểm tra giá sản phẩm theo loại giá nếu có cập nhật
      if (updateProductDto.price !== undefined && updateProductDto.typePrice !== undefined) {
        this.validationHelper.validateProductPrice(updateProductDto.price, updateProductDto.typePrice);
      }

      // Tìm sản phẩm theo ID và ID người dùng
      const product = await this.userProductRepository.findByIdAndUserId(
        id,
        userId,
      );

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Cập nhật các trường được cung cấp
      if (updateProductDto.name !== undefined) {
        product.name = updateProductDto.name;
      }

      if (updateProductDto.price !== undefined) {
        product.price = updateProductDto.price;
      }

      if (updateProductDto.typePrice !== undefined) {
        product.typePrice = updateProductDto.typePrice;
      }

      if (updateProductDto.description !== undefined) {
        product.description = updateProductDto.description;
      }

      // Xử lý thao tác với ảnh
      const imagesUploadUrls: Array<{ url: string; key: string; index: number }> = [];
      const now = Date.now();

      if (updateProductDto.images && updateProductDto.images.length > 0) {
        // Xử lý các thao tác DELETE trước
        const deleteOperations = updateProductDto.images.filter((img: any) => img.operation === 'DELETE');
        for (const deleteOp of deleteOperations) {
          // Xóa theo key nếu có
          if (deleteOp.key) {
            // Lọc ra các ảnh không bị xóa theo key
            const imageToDelete = product.images.find((img: any) => img.key === deleteOp.key);
            if (imageToDelete) {
              // Xóa ảnh trên S3
              await this.s3Service.deleteFile(imageToDelete.key);
              // Lọc ra các ảnh không bị xóa
              product.images = product.images.filter((img: any) => img.key !== deleteOp.key);
            }
          }
          // Xóa theo position nếu có
          else if (deleteOp.position !== undefined) {
            // Tìm ảnh cần xóa
            const imageToDelete = product.images.find((img: any) => img.position === deleteOp.position);
            if (imageToDelete) {
              // Xóa ảnh trên S3
              await this.s3Service.deleteFile(imageToDelete.key);
              // Lọc ra các ảnh không bị xóa
              product.images = product.images.filter((img: any) => img.position !== deleteOp.position);
            }
          }
        }

        // Xử lý các thao tác ADD
        const addOperations = updateProductDto.images.filter((img: any) => img.operation === 'ADD');
        for (const addOp of addOperations) {
          if (addOp.mimeType) {
            try {
              // Tìm vị trí lớn nhất hiện tại và tăng lên 1 để có vị trí mới
              const maxPosition = product.images.length > 0
                ? Math.max(...product.images.map((img: any) => img.position))
                : -1;
              const newPosition = maxPosition + 1;

              const mediaType = addOp.mimeType as ImageTypeEnum;

              // Tạo tên file với vị trí và timestamp
              const fileName = `product-image-${newPosition}-${now}`;

              // Sử dụng phương thức createImageUploadUrl
              const imageUploadUrl = await this.createImageUploadUrl(
                fileName,
                mediaType
              );

              const key = imageUploadUrl.key;
              const url = imageUploadUrl.url;

              this.logger.debug(`Created presigned URL for image upload: ${key} with position ${newPosition}`);

              // Thêm vào danh sách ảnh của sản phẩm với vị trí mới
              product.images.push({
                key,
                position: newPosition
              });

              // Thêm vào danh sách URL upload
              imagesUploadUrls.push({
                url,
                key,
                index: newPosition
              });
            } catch (error) {
              this.logger.error(`Failed to create image upload URL: ${error.message}`, error.stack);
            }
          }
        }
      }

      if (updateProductDto.tags !== undefined) {
        product.tags = updateProductDto.tags;
      }

      if (updateProductDto.shipmentConfig !== undefined) {
        product.shipmentConfig = updateProductDto.shipmentConfig;
      }

      // Xử lý custom fields nếu có
      if (updateProductDto.customFields !== undefined) {
        let customFields: any[] = [];
        if (updateProductDto.customFields && updateProductDto.customFields.length > 0) {
          this.logger.log(`Xử lý ${updateProductDto.customFields.length} custom fields cho sản phẩm`);

          // Lấy danh sách ID custom fields
          const customFieldIds = this.metadataHelper.extractCustomFieldIds(updateProductDto.customFields);

          // Lấy thông tin chi tiết custom fields từ database
          customFields = await this.customFieldRepository.findByIds(customFieldIds);

          // Validate custom fields
          this.metadataHelper.validateCustomFieldInputs(updateProductDto.customFields, customFields);
        }

        // Cập nhật metadata cho sản phẩm
        const metadata = this.metadataHelper.buildMetadata(
          updateProductDto.customFields,
          customFields
        );
        product.metadata = metadata;
      }

      // Đảm bảo các trường embedding vẫn là null để tránh lỗi vector dimension
      product.nameEmbedding = null;
      product.descriptionEmbedding = null;
      product.tagsEmbedding = null;

      // Cập nhật thời gian cập nhật
      product.updatedAt = Date.now();

      // Lưu sản phẩm vào database
      const updatedProduct = await this.userProductRepository.save(product);

      // Xử lý phân loại sản phẩm nếu có
      let classifications: ClassificationResponseDto[] = [];
      if (updateProductDto.classifications && updateProductDto.classifications.length > 0) {
        try {
          // Xử lý từng phân loại
          for (const classificationDto of updateProductDto.classifications) {
            if (classificationDto.id) {
              // Cập nhật phân loại hiện có
              const classification = await this.classificationService.update(
                classificationDto.id,
                classificationDto,
                userId
              );
              classifications.push(classification);
            } else {
              // Tạo phân loại mới
              // Kiểm tra type bắt buộc cho classification mới
              if (!classificationDto.type) {
                throw new AppException(
                  BUSINESS_ERROR_CODES.INVALID_INPUT,
                  'Loại phân loại (type) là bắt buộc khi tạo phân loại mới',
                );
              }

              // Chuyển đổi từ UpdateClassificationDto sang CreateClassificationDto
              const createClassificationDto: CreateClassificationDto = {
                type: classificationDto.type,
                price: classificationDto.price,
                customFields: classificationDto.customFields,
              };

              const classification = await this.classificationService.create(
                id,
                createClassificationDto,
                userId,
              );
              classifications.push(classification);
            }
          }
        } catch (classificationError) {
          // Ghi log nhưng không dừng việc cập nhật sản phẩm
          this.logger.warn(`Không thể cập nhật phân loại cho sản phẩm ${id}: ${classificationError.message}`);
        }
      }

      // Chuyển đổi thành DTO response và trả về
      const productResponse = await this.userProductHelper.mapToProductResponseDto(updatedProduct);

      // Thêm thông tin URL upload và phân loại vào response
      const response: any = { ...productResponse };

      // Thêm URL upload nếu có
      if (imagesUploadUrls.length > 0) {
        response.uploadUrls = {
          productId: updatedProduct.id.toString(),
          imagesUploadUrls
        };
      }

      // Thêm phân loại nếu có
      if (classifications.length > 0) {
        response.classifications = classifications;
      }

      return response;
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(
        `Lỗi khi cập nhật sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Tạo presigned URL cho việc tải lên hình ảnh sản phẩm
   * @param fileName Tên file
   * @param mediaType Loại media
   * @returns Thông tin URL và key
   */
  private async createImageUploadUrl(
    fileName: string,
    mediaType: ImageTypeEnum = ImageTypeEnum.PNG,
  ): Promise<{ key: string; url: string }> {
    try {
      // Tạo S3 key cho hình ảnh sản phẩm
      // Sử dụng cấu trúc thư mục giống với knowledge files và marketplace
      const key = generateS3Key({
        baseFolder: 'business',
        categoryFolder: CategoryFolderEnum.IMAGE,
        fileName: fileName || 'product-image',
        useTimeFolder: true,
      });

      // Tạo presigned URL
      const url = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        mediaType,
        FileSizeEnum.FIVE_MB,
      );

      return { key, url };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo URL upload hình ảnh: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo URL upload hình ảnh: ${error.message}`,
      );
    }
  }

  /**
   * Xóa sản phẩm (soft delete)
   * @param id ID của sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async deleteProduct(id: number, userId: number): Promise<void> {
    try {
      // Tìm sản phẩm theo ID và ID người dùng
      const product = await this.userProductRepository.findByIdAndUserId(
        id,
        userId,
      );

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Cập nhật trạng thái sản phẩm thành DELETED
      product.status = EntityStatusEnum.DELETED;
      product.updatedAt = Date.now();

      // Lưu sản phẩm vào database
      await this.userProductRepository.save(product);
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(`Lỗi khi xóa sản phẩm: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều sản phẩm (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều sản phẩm
   */
  @Transactional()
  async bulkDeleteProducts(
    bulkDeleteDto: BulkDeleteProductDto,
    userId: number,
  ): Promise<BulkDeleteProductResponseDto> {
    try {
      const { productIds } = bulkDeleteDto;
      const results: any[] = [];
      let successCount = 0;
      let failureCount = 0;

      this.logger.log(
        `Bắt đầu xóa bulk ${productIds.length} sản phẩm cho userId=${userId}`,
      );

      // Xử lý từng sản phẩm một để có thể báo cáo chi tiết
      for (const productId of productIds) {
        try {
          // Tìm sản phẩm theo ID và ID người dùng
          const product = await this.userProductRepository.findByIdAndUserId(
            productId,
            userId,
          );

          // Kiểm tra sản phẩm tồn tại
          if (!product) {
            results.push({
              productId,
              status: 'error',
              message: `Không tìm thấy sản phẩm với ID ${productId}`,
            });
            failureCount++;
            continue;
          }

          // Cập nhật trạng thái sản phẩm thành DELETED
          product.status = EntityStatusEnum.DELETED;
          product.updatedAt = Date.now();

          // Lưu sản phẩm vào database
          await this.userProductRepository.save(product);

          results.push({
            productId,
            status: 'success',
            message: 'Xóa sản phẩm thành công',
          });
          successCount++;

        } catch (error) {
          this.logger.error(
            `Lỗi khi xóa sản phẩm ${productId}: ${error.message}`,
            error.stack,
          );

          results.push({
            productId,
            status: 'error',
            message: error instanceof AppException ? error.message : `Lỗi khi xóa sản phẩm: ${error.message}`,
          });
          failureCount++;
        }
      }

      const response: BulkDeleteProductResponseDto = {
        totalRequested: productIds.length,
        successCount,
        failureCount,
        results,
        message: `Xóa thành công ${successCount}/${productIds.length} sản phẩm`,
      };

      this.logger.log(
        `Hoàn thành xóa bulk sản phẩm: ${successCount} thành công, ${failureCount} thất bại`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa bulk sản phẩm: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa bulk sản phẩm: ${error.message}`,
      );
    }
  }

  // ==================== INVENTORY MANAGEMENT METHODS ====================

  /**
   * Lấy thông tin tồn kho của sản phẩm theo kho
   * @param productId ID sản phẩm
   * @param warehouseId ID kho (optional)
   * @param userId ID người dùng
   * @returns Thông tin tồn kho
   */
  @Transactional()
  async getProductInventory(
    productId: number,
    warehouseId: number | null,
    userId: number,
  ): Promise<InventoryResponseDto[]> {
    try {
      // Kiểm tra sản phẩm có tồn tại và thuộc về user
      const product = await this.userProductRepository.findByIdAndUserId(productId, userId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          'Sản phẩm không tồn tại hoặc không thuộc về bạn',
        );
      }

      // Tạo query để lấy inventory
      const queryDto = new QueryInventoryDto();
      queryDto.productId = productId;
      queryDto.userId = userId;
      if (warehouseId) {
        queryDto.warehouseId = warehouseId;
      }

      // Lấy danh sách inventory
      const inventoryResult = await this.inventoryRepository.findAll(queryDto);

      // Chuyển đổi sang DTO response
      const inventoryDtos = await Promise.all(
        inventoryResult.items.map(async (inventory) => {
          const dto = new InventoryResponseDto();
          dto.id = inventory.id;
          dto.productId = inventory.productId;
          dto.warehouseId = inventory.warehouseId;
          dto.currentQuantity = inventory.currentQuantity;
          dto.totalQuantity = inventory.totalQuantity;
          dto.availableQuantity = inventory.availableQuantity;
          dto.reservedQuantity = inventory.reservedQuantity;
          dto.defectiveQuantity = inventory.defectiveQuantity;
          dto.lastUpdated = inventory.lastUpdated;

          // Lấy thông tin warehouse nếu có
          if (inventory.warehouseId) {
            const warehouse = await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(inventory.warehouseId);
            if (warehouse) {
              dto.warehouse = {
                warehouseId: warehouse.warehouseId,
                name: warehouse.name,
                description: warehouse.description,
                type: warehouse.type,
                address: warehouse.address,
                capacity: warehouse.capacity,
              };
            }
          }

          return dto;
        })
      );

      return inventoryDtos;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy thông tin tồn kho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_FETCH_FAILED,
        `Lỗi khi lấy thông tin tồn kho: ${error.message}`,
      );
    }
  }

  /**
   * Tạo hoặc cập nhật tồn kho cho sản phẩm
   * @param productId ID sản phẩm
   * @param inventoryData Dữ liệu tồn kho
   * @param userId ID người dùng
   * @returns Thông tin tồn kho đã tạo/cập nhật
   */
  @Transactional()
  async createOrUpdateProductInventory(
    productId: number,
    inventoryData: ProductInventoryDto,
    userId: number,
  ): Promise<InventoryResponseDto> {
    try {
      // Kiểm tra sản phẩm có tồn tại và thuộc về user
      const product = await this.userProductRepository.findByIdAndUserId(productId, userId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          'Sản phẩm không tồn tại hoặc không thuộc về bạn',
        );
      }

      // Kiểm tra warehouse có tồn tại
      if (inventoryData.warehouseId) {
        const warehouse = await this.physicalWarehouseRepository.findByWarehouseId_user(inventoryData.warehouseId);
        if (!warehouse) {
          throw new AppException(
            BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
            'Kho vật lý không tồn tại',
          );
        }
      }

      // Kiểm tra SKU không được trùng với sản phẩm của user đang đăng nhập
      if (inventoryData.sku) {
        await this.validationHelper.validateSkuUniqueness(inventoryData.sku, userId);
      }

      // Kiểm tra xem đã có inventory cho sản phẩm và kho này chưa
      const existingInventory = await this.inventoryRepository.findByProductAndWarehouseNullable(
        productId,
        inventoryData.warehouseId || null,
      );

      let inventory: Inventory;

      if (existingInventory) {
        // Cập nhật inventory hiện có
        existingInventory.availableQuantity = inventoryData.availableQuantity || 0;
        existingInventory.sku = inventoryData.sku || null;
        existingInventory.barcode = inventoryData.barcode || null;

        // Sử dụng helper để tính toán số lượng
        this.validationHelper.calculateInventoryQuantities(existingInventory);
        existingInventory.lastUpdated = Date.now();

        inventory = await this.inventoryRepository.save(existingInventory);
      } else {
        // Tạo inventory mới
        const newInventory = new Inventory();
        newInventory.productId = productId;
        newInventory.warehouseId = inventoryData.warehouseId || null;
        newInventory.availableQuantity = inventoryData.availableQuantity || 0;
        newInventory.sku = inventoryData.sku || null;
        newInventory.barcode = inventoryData.barcode || null;

        // Sử dụng helper để tính toán số lượng
        this.validationHelper.calculateInventoryQuantities(newInventory);
        newInventory.lastUpdated = Date.now();

        inventory = await this.inventoryRepository.save(newInventory);
      }

      // Chuyển đổi sang DTO response
      const dto = new InventoryResponseDto();
      dto.id = inventory.id;
      dto.productId = inventory.productId;
      dto.warehouseId = inventory.warehouseId;
      dto.currentQuantity = inventory.currentQuantity;
      dto.totalQuantity = inventory.totalQuantity;
      dto.availableQuantity = inventory.availableQuantity;
      dto.reservedQuantity = inventory.reservedQuantity;
      dto.defectiveQuantity = inventory.defectiveQuantity;
      dto.lastUpdated = inventory.lastUpdated;
      dto.sku = inventory.sku;
      dto.barcode = inventory.barcode;

      // Lấy thông tin warehouse nếu có
      if (inventory.warehouseId) {
        const warehouse = await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(inventory.warehouseId);
        if (warehouse) {
          dto.warehouse = {
            warehouseId: warehouse.warehouseId,
            name: warehouse.name,
            description: warehouse.description,
            type: warehouse.type,
            address: warehouse.address,
            capacity: warehouse.capacity,
          };
        }
      }

      return dto;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi tạo/cập nhật tồn kho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Lỗi khi tạo/cập nhật tồn kho: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách kho vật lý để chọn
   * @returns Danh sách kho vật lý
   */
  async getWarehouseList(): Promise<WarehouseListDto[]> {
    try {
      // Lấy tất cả kho vật lý với thông tin đầy đủ
      const result = await this.physicalWarehouseRepository.findAll({
        page: 1,
        limit: 1000, // Lấy nhiều để có đủ kho cho user chọn
        sortBy: 'warehouseId',
        sortDirection: 'ASC',
      });

      // Chuyển đổi sang DTO
      return result.items.map(warehouse => {
        const dto = new WarehouseListDto();
        dto.warehouseId = warehouse.warehouseId;
        dto.name = warehouse.name;
        dto.description = warehouse.description;
        dto.type = warehouse.type;
        dto.address = warehouse.address;
        dto.capacity = warehouse.capacity;
        return dto;
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách kho vật lý: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_FIND_FAILED,
        `Lỗi khi lấy danh sách kho vật lý: ${error.message}`,
      );
    }
  }
}